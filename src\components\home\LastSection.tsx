import React from 'react';

const LastSection: React.FC = () => {
  return (
    <div
      className="relative flex items-center justify-center w-full min-h-screen lg:h-[1000px] md:h-[800px] h-[700px] bg-no-repeat bg-cover bg-center"
      style={{
        backgroundImage:
          "url('https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2FIMG_3269.webp?alt=media&token=0bf2d44e-71b0-457b-8626-7c22def44636')",
      }}
    >
      {/* Luxury gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#16191D]/80 via-[#16191D]/70 to-[#16191D]/90"></div>

      {/* Luxury particles effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="luxury-particle absolute top-1/4 left-1/4 w-2 h-2 bg-[#D4C2A4]/30 rounded-full shadow-lg shadow-[#D4C2A4]/20"></div>
        <div className="luxury-particle absolute top-3/4 right-1/4 w-1 h-1 bg-[#D4C2A4]/40 rounded-full shadow-lg shadow-[#D4C2A4]/30"></div>
        <div className="luxury-particle absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-[#D4C2A4]/20 rounded-full shadow-lg shadow-[#D4C2A4]/15"></div>
        <div className="luxury-particle absolute top-1/3 right-1/3 w-1 h-1 bg-[#D4C2A4]/25 rounded-full shadow-lg shadow-[#D4C2A4]/20"></div>
        <div className="luxury-particle absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-[#D4C2A4]/35 rounded-full shadow-lg shadow-[#D4C2A4]/25"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center w-full max-w-7xl px-3 sm:px-4 md:px-6 lg:px-8">
        <div className="luxury-glass-container w-full max-w-5xl px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-6 sm:py-8 md:py-10 lg:py-12 xl:py-16 rounded-xl sm:rounded-2xl">
          <p className="font-open-sans text-[10px] xs:text-xs sm:text-sm tracking-[0.2em] sm:tracking-[0.3em] mb-3 sm:mb-4 md:mb-6 text-left text-[#D4C2A4] uppercase luxury-glow-text">
            CUSTOM SAFARI
          </p>
          <h1 className="font-cormorant text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-[70px] mb-4 sm:mb-6 md:mb-8 leading-tight text-left text-[#F2EEE6] luxury-typography">
            Let's Transform your <em className="italic text-[#D4C2A4]">Dream<br className="hidden xs:block" /><span className="xs:hidden"> </span>Safari</em> into Reality
          </h1>
          <p className="font-open-sans text-xs xs:text-sm sm:text-base md:text-lg text-[#F2EEE6]/90 mb-6 sm:mb-8 md:mb-10 max-w-4xl text-left leading-relaxed luxury-typography">
            Got a vision for your safari? We're all ears! Tell us your must-haves—maybe a private game
            drive in Ngorongoro or a beach escape in Zanzibar—and we'll craft an adventure that's 100% you.
            Our expert planners know Tanzania like the back of their hand, so you'll get the best
            spots, top-notch guides, and all the luxury you deserve.
          </p>
          <div className="flex flex-col sm:flex-row justify-start items-stretch sm:items-start gap-3 sm:gap-4 md:gap-6">
            <button className="luxury-button w-full sm:w-auto bg-gradient-to-r from-[#D4C2A4] to-[#C4B294] text-[#16191D] py-3 sm:py-4 px-6 sm:px-8 text-xs sm:text-sm font-open-sans font-semibold tracking-[0.1em] sm:tracking-[0.15em] uppercase rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-[#D4C2A4]/25 hover:-translate-y-1 relative overflow-hidden min-h-[48px] flex items-center justify-center">
              <span className="relative z-10">PLAN YOUR SAFARI</span>
            </button>
            <button className="w-full sm:w-auto text-[#F2EEE6] py-3 sm:py-4 px-6 sm:px-8 text-xs sm:text-sm font-open-sans font-medium tracking-[0.1em] sm:tracking-[0.15em] uppercase border-2 border-[#D4C2A4]/40 hover:border-[#D4C2A4] rounded-lg transition-all duration-300 hover:bg-[#D4C2A4]/10 hover:shadow-lg hover:shadow-[#D4C2A4]/20 luxury-border-glow min-h-[48px] flex items-center justify-center">
              CALL OUR EXPERT
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LastSection;