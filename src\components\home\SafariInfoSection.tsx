import React from 'react';

const SafariInfoSection = () => {
  return (
    <>
      {/* Luxury VVIP Styles */}
      <style>{`
        /* VIP Glow Effects */
        @keyframes luxuryGlow {
          0%, 100% {
            text-shadow:
              0 0 10px rgba(212, 194, 164, 0.4),
              0 0 20px rgba(212, 194, 164, 0.2),
              0 0 30px rgba(212, 194, 164, 0.1);
          }
          50% {
            text-shadow:
              0 0 15px rgba(212, 194, 164, 0.6),
              0 0 30px rgba(212, 194, 164, 0.4),
              0 0 45px rgba(212, 194, 164, 0.2);
          }
        }

        .luxury-glow-text {
          animation: luxuryGlow 4s ease-in-out infinite;
        }

        /* Premium Glass Morphism Effects */
        .luxury-glass-container {
          background: linear-gradient(135deg,
            rgba(212, 194, 164, 0.15) 0%,
            rgba(212, 194, 164, 0.08) 50%,
            rgba(212, 194, 164, 0.12) 100%);
          backdrop-filter: blur(25px) saturate(180%);
          -webkit-backdrop-filter: blur(25px) saturate(180%);
          border: 1px solid rgba(212, 194, 164, 0.3);
          box-shadow:
            0 25px 50px rgba(22, 25, 29, 0.4),
            0 0 0 1px rgba(212, 194, 164, 0.1),
            inset 0 1px 0 rgba(212, 194, 164, 0.2),
            inset 0 -1px 0 rgba(212, 194, 164, 0.1);
          position: relative;
          overflow: hidden;
        }

        .luxury-glass-container::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle at 30% 20%, rgba(212, 194, 164, 0.1) 0%, transparent 50%);
          pointer-events: none;
        }

        /* Premium Button Enhancements */
        .luxury-button {
          position: relative;
          overflow: hidden;
          background: linear-gradient(135deg, #D4C2A4 0%, #C4B294 50%, #D4C2A4 100%);
          background-size: 200% 100%;
          transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
          box-shadow:
            0 8px 25px rgba(212, 194, 164, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(212, 194, 164, 0.4);
        }

        .luxury-button::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent);
          transition: left 0.6s ease;
        }

        .luxury-button:hover::before {
          left: 100%;
        }

        .luxury-button:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow:
            0 12px 35px rgba(212, 194, 164, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
          background-position: 100% 0;
        }

        /* Premium Typography Effects */
        .luxury-typography {
          font-feature-settings: "liga" 1, "kern" 1;
          text-rendering: optimizeLegibility;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          letter-spacing: 0.02em;
        }

        /* VIP Status Indicators */
        .vip-indicator {
          position: relative;
        }

        .vip-indicator::after {
          content: '';
          position: absolute;
          top: -8px;
          right: -8px;
          width: 12px;
          height: 12px;
          background: radial-gradient(circle, #D4C2A4 0%, #C4B294 100%);
          border-radius: 50%;
          box-shadow: 0 0 15px rgba(212, 194, 164, 0.8);
          animation: luxuryGlow 3s ease-in-out infinite;
        }

        /* Elegant Border Animations */
        @keyframes luxuryBorderGlow {
          0%, 100% {
            border-color: rgba(212, 194, 164, 0.4);
            box-shadow: 0 0 15px rgba(212, 194, 164, 0.2);
          }
          50% {
            border-color: rgba(212, 194, 164, 0.8);
            box-shadow: 0 0 30px rgba(212, 194, 164, 0.4);
          }
        }

        .luxury-border-glow {
          animation: luxuryBorderGlow 4s ease-in-out infinite;
        }

        /* Enhanced Responsive Design for Smartphones */
        @media (max-width: 768px) {
          .luxury-glass-container {
            backdrop-filter: blur(20px) saturate(150%);
            -webkit-backdrop-filter: blur(20px) saturate(150%);
            margin: 0.75rem;
            border-radius: 1.25rem;
            padding: 1.5rem !important;
          }

          .luxury-button:hover {
            transform: translateY(-1px) scale(1.01);
          }

          .vip-indicator::after {
            width: 6px;
            height: 6px;
            top: -3px;
            right: -3px;
          }
        }

        @media (max-width: 640px) {
          .luxury-glass-container {
            margin: 0.5rem;
            border-radius: 1rem;
            padding: 1.25rem !important;
          }

          .luxury-button {
            padding: 0.75rem 1.5rem !important;
            font-size: 0.75rem !important;
            letter-spacing: 2px !important;
          }

          .vip-indicator::after {
            width: 5px;
            height: 5px;
            top: -2px;
            right: -2px;
          }
        }

        @media (max-width: 480px) {
          .luxury-glass-container {
            margin: 0.25rem;
            border-radius: 0.75rem;
            padding: 1rem !important;
          }

          .luxury-button {
            padding: 0.625rem 1.25rem !important;
            font-size: 0.7rem !important;
            letter-spacing: 1px !important;
          }

          .vip-indicator::after {
            width: 4px;
            height: 4px;
            top: -2px;
            right: -2px;
          }
        }
      `}</style>

      <div className="py-8 sm:py-12 md:py-16 lg:py-24" style={{ background: '#16191D' }}>
        {/* Top Section: "Experts Block" */}
        <section className="max-w-[900px] mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="luxury-glass-container luxury-border-glow px-4 sm:px-6 md:px-8 lg:px-16 py-8 sm:py-12 md:py-16 lg:py-24 rounded-2xl sm:rounded-3xl relative overflow-hidden">
            {/* Decorative Elements */}
            <div className="absolute top-4 sm:top-6 md:top-8 right-4 sm:right-6 md:right-8 w-12 sm:w-16 md:w-20 h-12 sm:h-16 md:h-20 opacity-20">
              <svg viewBox="0 0 24 24" fill="none" className="w-full h-full" style={{ color: '#D4C2A4' }}>
                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z" fill="currentColor"/>
              </svg>
            </div>

            {/* VIP SERVICE Label */}
            <div className="vip-indicator inline-block mb-3 sm:mb-4 md:mb-6">
              <p className="luxury-typography uppercase tracking-[2px] sm:tracking-[4px] md:tracking-[6px] text-[10px] sm:text-xs md:text-sm font-semibold"
                 style={{
                   color: '#D4C2A4',
                   fontFamily: 'Open Sans, sans-serif'
                 }}>
                ✦ ELITE VIP SERVICE ✦
              </p>
            </div>

            {/* Main Heading */}
            <h1 className="luxury-glow-text luxury-typography font-light text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl leading-[1.1] mb-4 sm:mb-6 md:mb-8"
                style={{
                  color: '#F2EEE6',
                  fontFamily: 'Cormorant Garamond, serif'
                }}>
              Need Exclusive<br />
              <em className="italic font-normal">Royal</em> Assistance?
            </h1>

            {/* Paragraph */}
            <p className="luxury-typography text-sm sm:text-base md:text-lg lg:text-xl leading-[1.6] sm:leading-[1.7] md:leading-[1.8] max-w-[650px] mb-6 sm:mb-8 md:mb-12 opacity-90"
               style={{
                 color: '#F2EEE6',
                 fontFamily: 'Open Sans, sans-serif'
               }}>
              Experience unparalleled luxury with our dedicated concierge team. Our elite safari specialists
              provide 24/7 premium support for discerning travelers seeking extraordinary African adventures.
              From bespoke itineraries to exclusive access, we ensure your journey exceeds every expectation.
            </p>

            {/* Call to Action Button */}
            <button className="luxury-button luxury-typography font-bold uppercase tracking-[1px] sm:tracking-[2px] md:tracking-[3px] px-6 sm:px-8 md:px-10 lg:px-14 py-3 sm:py-4 md:py-5 rounded-lg sm:rounded-xl text-xs sm:text-sm md:text-base transition-all duration-500"
                    style={{
                      color: '#16191D',
                      fontFamily: 'Open Sans, sans-serif'
                    }}>
              CONTACT VIP CONCIERGE
            </button>
          </div>
        </section>

        {/* Bottom Section: "Safaris Block" */}
        <section className="max-w-[1400px] mx-auto mt-8 sm:mt-12 md:mt-16 lg:mt-32 flex flex-col lg:flex-row items-center px-3 sm:px-4 md:px-6 lg:px-8 gap-6 sm:gap-8 md:gap-12 lg:gap-20">
          {/* Left Column - Image */}
          <div className="w-full lg:flex-[0_0_60%] relative">
            <div className="luxury-glass-container luxury-border-glow w-full h-[250px] sm:h-[300px] md:h-[400px] lg:h-[550px] xl:h-[650px] overflow-hidden rounded-2xl sm:rounded-3xl relative">
              <img
                src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Flion.webp?alt=media&token=6670b470-31c0-46ae-9d67-880b24ef49b4"
                alt="Majestic African wildlife landscape"
                className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
                onLoad={() => console.log('✅ Image loaded!')}
                onError={(e) => {
                  console.log('❌ Trying fallback image...');
                  
                }}
              />
              {/* Image Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

              {/* VIP Badge */}
              <div className="absolute top-3 sm:top-4 md:top-6 left-3 sm:left-4 md:left-6 luxury-glass-container px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded-full">
                <span className="luxury-typography text-[10px] sm:text-xs font-bold uppercase tracking-[1px] sm:tracking-[2px]"
                      style={{ color: '#D4C2A4', fontFamily: 'Open Sans, sans-serif' }}>
                  ✦ PREMIUM EXPERIENCE
                </span>
              </div>
            </div>
          </div>

          {/* Right Column - Text Content */}
          <div className="w-full lg:flex-1 text-center lg:text-left">
            {/* ROYAL SAFARIS Label */}
            <div className="vip-indicator inline-block mb-4 sm:mb-6 md:mb-8">
              <p className="luxury-typography uppercase tracking-[2px] sm:tracking-[4px] md:tracking-[6px] text-[10px] sm:text-xs md:text-sm lg:text-base font-bold opacity-80"
                 style={{
                   color: '#D4C2A4',
                   fontFamily: 'Open Sans, sans-serif'
                 }}>
                ✦ ROYAL SAFARIS ✦
              </p>
            </div>

            {/* Main Heading */}
            <h2 className="luxury-glow-text luxury-typography font-light text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl leading-[0.9] mb-6 sm:mb-8 md:mb-12"
                style={{
                  color: '#F2EEE6',
                  fontFamily: 'Cormorant Garamond, serif'
                }}>
              DISCOVER<br />
              <em className="italic font-normal">ELITE</em><br />
              SAFARI<br />
              <span style={{ color: '#D4C2A4' }}>LUXURY</span>
            </h2>

            {/* Decorative Arrow */}
            <div className="flex justify-center lg:justify-start mb-4 sm:mb-6 md:mb-8">
              <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 luxury-glass-container rounded-full flex items-center justify-center">
                <svg viewBox="0 0 24 24" fill="none" className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8" style={{ color: '#D4C2A4' }}>
                  <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>

            {/* Premium Description */}
            <p className="luxury-typography text-sm sm:text-base md:text-lg lg:text-xl leading-[1.6] sm:leading-[1.7] md:leading-[1.8] max-w-[500px] mx-auto lg:mx-0 opacity-90"
               style={{
                 color: '#F2EEE6',
                 fontFamily: 'Open Sans, sans-serif'
               }}>
              Embark on extraordinary journeys through Africa's most pristine wilderness. Our curated collection
              of elite safari experiences offers unmatched luxury, exclusive access, and personalized service
              for the most discerning travelers.
            </p>
          </div>
        </section>
      </div>
    </>
  );
};

export default SafariInfoSection;
